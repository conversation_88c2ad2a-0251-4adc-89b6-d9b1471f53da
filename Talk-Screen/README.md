# Talk Screen AI

音声チャットとスクリーンシェアを組み合わせたAIアシスタントアプリケーション。最新のGemini 2.5 Flash Native Audio Live APIを使用して、AIと自然な音声対話を行いながら、画面を共有することができます。

## 機能

- **ネイティブオーディオ対話**: Gemini 2.5 Flash Native Audio Live APIによる自然で表現豊かな音声対話
- **思考機能**: AIの思考プロセスを可視化（thinking capabilities）
- **音声による双方向コミュニケーション**: 低遅延でリアルタイムな音声対話
- **リアルタイムスクリーンシェア**: 画面内容をAIと共有
- **テキスト入力サポート**: 音声とテキストの両方でコミュニケーション可能
- **高品質な音声合成**: より自然で人間らしい音声出力
- **エラーハンドリングとログ機能**: 堅牢なエラー処理とデバッグ支援

## 必要条件

- Python 3.11以上
- マイク入力デバイス
- スピーカー出力デバイス
- Gemini API キー（Gemini 2.5 Flash Native Audio Live API対応）
- 安定したインターネット接続（Live API使用のため）

## インストール

1. リポジトリをクローン:
```bash
git clone https://github.com/yourusername/Talk-Screen-AI.git
cd Talk-Screen-AI
```

2. 依存パッケージをインストール:
```bash
pip install -r requirements.txt
```

3. 環境変数の設定:
`.env`ファイルをプロジェクトのルートディレクトリに作成し、以下の内容を設定:
```
GEMINI_API_KEY=your_api_key_here
SYSTEM_PROMPT=カスタムシステムプロンプト（オプション）
```

## 使用方法

1. アプリケーションを起動:
```bash
python -m src.main

or

PYTHONPATH=. python src/main.py

```

2. 操作方法:
- **音声で話しかける**: マイクに向かって話すだけで、Gemini 2.5 Flash Native Audio Live APIがリアルタイムで自然な音声で応答します
- **思考プロセスの確認**: AIの思考プロセスがログに表示され、どのように回答を生成しているかを確認できます
- **テキスト入力**: プロンプトに直接テキストを入力することもできます
- **画面共有**: アプリケーション実行中は自動的に画面がAIと共有され、画面内容について質問できます
- **終了**: 'q'を入力してEnterを押すと終了します

## プロジェクト構造

```
Talk-Screen-AI/
├── src/
│   ├── audio/
│   │   └── audio_handler.py    # 音声入出力の処理
│   ├── screen/
│   │   └── screen_capture.py   # スクリーンキャプチャの処理
│   ├── config/
│   │   └── settings.py         # アプリケーション設定
│   ├── logger.py               # ログ機能
│   └── main.py                 # メインアプリケーション
├── .env                        # 環境変数
├── requirements.txt            # 依存パッケージ
└── README.md                   # このファイル
```

## エラーハンドリング

アプリケーションは以下の状況で適切なエラーハンドリングを行います：

- マイクやスピーカーデバイスの初期化エラー
- ネットワーク接続の問題
- API呼び出しの失敗
- 音声ストリームの問題

エラーが発生した場合は、`app.log`ファイルに詳細が記録されます。

## 注意事項

- マイクとスピーカーのデバイス設定が正しく行われていることを確認してください
- **Live API使用**: Gemini 2.5 Flash Native Audio Live APIを使用するため、安定したインターネット接続が必要です
- **セッション制限**: ネイティブオーディオモデルは128kトークンのコンテキスト制限があります
- **音声品質**: 最適な音声品質のため、16kHz、16-bit PCM、モノラル形式で音声を送信します
- **APIキー**: Gemini 2.5 Flash Native Audio Live API対応のAPIキーが必要です
- **利用制限**: APIキーの利用制限と料金に注意してください

## ライセンス

MITライセンス
