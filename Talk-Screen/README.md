# Talk Screen AI

音声チャットとスクリーンシェアを組み合わせたAIアシスタントアプリケーション。AIと会話しながら、画面を共有することができます。

## 機能

- 音声による双方向コミュニケーション
- リアルタイムスクリーンシェア
- テキスト入力サポート
- 高品質な音声合成
- エラーハンドリングとログ機能

## 必要条件

- Python 3.11以上
- マイク入力デバイス
- スピーカー出力デバイス
- Gemini API キー

## インストール

1. リポジトリをクローン:
```bash
git clone https://github.com/yourusername/Talk-Screen-AI.git
cd Talk-Screen-AI
```

2. 依存パッケージをインストール:
```bash
pip install -r requirements.txt
```

3. 環境変数の設定:
`.env`ファイルをプロジェクトのルートディレクトリに作成し、以下の内容を設定:
```
GEMINI_API_KEY=your_api_key_here
SYSTEM_PROMPT=カスタムシステムプロンプト（オプション）
```

## 使用方法

1. アプリケーションを起動:
```bash
python -m src.main

or

PYTHONPATH=. python src/main.py

```

2. 操作方法:
- 音声で話しかける: マイクに向かって話すだけで、AIがリアルタイムで応答します
- テキスト入力: プロンプトに直接テキストを入力することもできます
- 終了: 'q'を入力してEnterを押すと終了します

## プロジェクト構造

```
Talk-Screen-AI/
├── src/
│   ├── audio/
│   │   └── audio_handler.py    # 音声入出力の処理
│   ├── screen/
│   │   └── screen_capture.py   # スクリーンキャプチャの処理
│   ├── config/
│   │   └── settings.py         # アプリケーション設定
│   ├── logger.py               # ログ機能
│   └── main.py                 # メインアプリケーション
├── .env                        # 環境変数
├── requirements.txt            # 依存パッケージ
└── README.md                   # このファイル
```

## エラーハンドリング

アプリケーションは以下の状況で適切なエラーハンドリングを行います：

- マイクやスピーカーデバイスの初期化エラー
- ネットワーク接続の問題
- API呼び出しの失敗
- 音声ストリームの問題

エラーが発生した場合は、`app.log`ファイルに詳細が記録されます。

## 注意事項

- マイクとスピーカーのデバイス設定が正しく行われていることを確認してください
- 安定したインターネット接続が必要です
- APIキーの利用制限に注意してください

## ライセンス

MITライセンス
